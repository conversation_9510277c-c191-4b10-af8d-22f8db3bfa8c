// إدارة قاعدة البيانات المحلية
class DatabaseManager {
    constructor() {
        this.dbName = 'kimaWarehouse';
        this.version = 3; // زيادة رقم الإصدار لإضافة فهرس itemCode
        this.db = null;
        this.init();
    }

    async init() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.version);

            request.onerror = () => {
                console.error('خطأ في فتح قاعدة البيانات:', request.error);
                reject(request.error);
            };

            request.onsuccess = () => {
                this.db = request.result;
                console.log('تم فتح قاعدة البيانات بنجاح');
                resolve(this.db);
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                const oldVersion = event.oldVersion;

                // إنشاء جدول الأصناف
                if (!db.objectStoreNames.contains('items')) {
                    const itemStore = db.createObjectStore('items', {
                        keyPath: 'id',
                        autoIncrement: true
                    });

                    // إنشاء فهارس للبحث السريع
                    itemStore.createIndex('name', 'name', { unique: false });
                    itemStore.createIndex('code', 'code', { unique: true });
                    itemStore.createIndex('itemCode', 'itemCode', { unique: true });
                    itemStore.createIndex('supplier', 'supplierName', { unique: false });
                    itemStore.createIndex('supplyDate', 'supplyDate', { unique: false });
                } else if (oldVersion < 3) {
                    // تحديث قاعدة البيانات الموجودة لإضافة فهرس itemCode
                    const transaction = event.target.transaction;
                    const itemStore = transaction.objectStore('items');

                    // إضافة فهرس itemCode إذا لم يكن موجود
                    if (!itemStore.indexNames.contains('itemCode')) {
                        itemStore.createIndex('itemCode', 'itemCode', { unique: true });
                        console.log('تم إضافة فهرس itemCode');
                    }
                }

                // إنشاء جدول الإعدادات
                if (!db.objectStoreNames.contains('settings')) {
                    db.createObjectStore('settings', { keyPath: 'key' });
                }

                // إنشاء جدول تحديثات بيان الصرف
                if (!db.objectStoreNames.contains('dispensingUpdates')) {
                    const dispensingStore = db.createObjectStore('dispensingUpdates', {
                        keyPath: 'id',
                        autoIncrement: false
                    });

                    // إنشاء فهارس
                    dispensingStore.createIndex('itemId', 'itemId', { unique: false });
                    dispensingStore.createIndex('type', 'type', { unique: false });
                    dispensingStore.createIndex('date', 'date', { unique: false });
                    dispensingStore.createIndex('responsible', 'responsible', { unique: false });
                }

                console.log('تم إنشاء قاعدة البيانات');
            };
        });
    }

    // إضافة صنف جديد
    async addItem(item) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['items'], 'readwrite');
            const store = transaction.objectStore('items');

            // إضافة تاريخ الإنشاء
            item.createdAt = new Date().toISOString();
            item.updatedAt = new Date().toISOString();

            const request = store.add(item);

            request.onsuccess = () => {
                resolve(request.result);
            };

            request.onerror = () => {
                reject(request.error);
            };
        });
    }

    // تحديث صنف
    async updateItem(id, item) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['items'], 'readwrite');
            const store = transaction.objectStore('items');

            // الحصول على الصنف الحالي أولاً
            const getRequest = store.get(id);

            getRequest.onsuccess = () => {
                const existingItem = getRequest.result;
                if (existingItem) {
                    // دمج البيانات الجديدة مع الموجودة
                    const updatedItem = {
                        ...existingItem,
                        ...item,
                        id: id,
                        updatedAt: new Date().toISOString()
                    };

                    const putRequest = store.put(updatedItem);

                    putRequest.onsuccess = () => {
                        resolve(updatedItem);
                    };

                    putRequest.onerror = () => {
                        reject(putRequest.error);
                    };
                } else {
                    reject(new Error('الصنف غير موجود'));
                }
            };

            getRequest.onerror = () => {
                reject(getRequest.error);
            };
        });
    }

    // حذف صنف
    async deleteItem(id) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['items'], 'readwrite');
            const store = transaction.objectStore('items');

            const request = store.delete(id);

            request.onsuccess = () => {
                resolve(true);
            };

            request.onerror = () => {
                reject(request.error);
            };
        });
    }

    // الحصول على جميع الأصناف
    async getAllItems() {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['items'], 'readonly');
            const store = transaction.objectStore('items');

            const request = store.getAll();

            request.onsuccess = () => {
                resolve(request.result);
            };

            request.onerror = () => {
                reject(request.error);
            };
        });
    }

    // البحث في الأصناف
    async searchItems(query) {
        try {
            const allItems = await this.getAllItems();

            if (!query || query.trim() === '') {
                return allItems;
            }

            const searchTerm = query.toLowerCase().trim();

            return allItems.filter(item => {
                try {
                    // البحث في اسم الصنف - يجب أن يكون في بداية الكلمة
                    let nameMatch = false;
                    if (item.name) {
                        const itemName = item.name.toLowerCase();
                        // البحث في بداية الاسم أو بداية أي كلمة بعد مسافة
                        nameMatch = itemName.startsWith(searchTerm) ||
                                   itemName.includes(' ' + searchTerm);
                    }

                    // البحث في كود الصنف - يجب أن يكون في بداية الكود
                    let codeMatch = false;
                    if (item.itemCode) {
                        const itemCode = item.itemCode.toLowerCase();
                        codeMatch = itemCode.startsWith(searchTerm);
                    }

                    return nameMatch || codeMatch;
                } catch (error) {
                    console.error('خطأ في فلترة الصنف:', item, error);
                    return false;
                }
            });
        } catch (error) {
            console.error('خطأ في دالة البحث:', error);
            throw error;
        }
    }

    // الحصول على صنف بالمعرف
    async getItem(id) {
        return new Promise((resolve, reject) => {
            // التحقق من صحة المعرف
            if (!id || (typeof id !== 'number' && typeof id !== 'string')) {
                console.error('معرف غير صحيح:', id);
                resolve(null);
                return;
            }

            // تحويل المعرف إلى رقم إذا كان نص
            const numericId = typeof id === 'string' ? parseInt(id) : id;

            if (isNaN(numericId)) {
                console.error('معرف غير صحيح بعد التحويل:', id);
                resolve(null);
                return;
            }

            console.log('البحث عن الصنف بالمعرف:', numericId);

            const transaction = this.db.transaction(['items'], 'readonly');
            const store = transaction.objectStore('items');

            const request = store.get(numericId);

            request.onsuccess = () => {
                console.log('نتيجة البحث:', request.result);
                resolve(request.result);
            };

            request.onerror = () => {
                console.error('خطأ في البحث عن الصنف:', request.error);
                reject(request.error);
            };
        });
    }



    // الحصول على إحصائيات
    async getStatistics() {
        const items = await this.getAllItems();

        const totalItems = items.length;
        const suppliers = [...new Set(items.map(item => item.supplierName).filter(Boolean))];
        const totalSuppliers = suppliers.length;

        return {
            totalItems,
            totalSuppliers
        };
    }

    // تصدير البيانات
    async exportData() {
        const items = await this.getAllItems();
        return items.map(item => ({
            'اسم الصنف': item.name || '',
            'الكود': item.itemCode || '',
            'المواصفات': item.specs || '',
            'رقم الصنف المخزني': item.code || '',
            'الرصيد': item.stock || 0,
            'رقم الشلف': item.shelfNumber || '',
            'رقم العين': item.eyeNumber || '',
            'اسم المورد': item.supplierName || '',
            'تاريخ التوريد': item.supplyDate || ''
        }));
    }

    // استيراد البيانات
    async importData(data) {
        let successCount = 0;
        let errorCount = 0;
        let updatedCount = 0;
        const errors = [];

        console.log(`🔍 بدء استيراد ${data.length} صنف من Excel`);

        for (let i = 0; i < data.length; i++) {
            const row = data[i];

            try {
                const item = {
                    name: row['اسم الصنف'] || row['name'] || '',
                    itemCode: row['الكود'] || row['itemCode'] || '',
                    specs: row['المواصفات'] || row['specs'] || '',
                    code: row['رقم الصنف المخزني'] || row['code'] || '',
                    stock: parseInt(row['الرصيد'] || row['stock'] || 0),
                    shelfNumber: row['رقم الشلف'] || row['shelfNumber'] || '',
                    eyeNumber: row['رقم العين'] || row['eyeNumber'] || '',
                    supplierName: row['اسم المورد'] || row['supplierName'] || '',
                    supplyDate: row['تاريخ التوريد'] || row['supplyDate'] || '',
                    updatedAt: new Date().toISOString()
                };

                console.log(`📋 الصف ${i + 1}: اسم="${item.name}", كود="${item.itemCode}", رقم مخزني="${item.code}"`);

                // التحقق من وجود البيانات المطلوبة
                if (!item.name || !item.itemCode || !item.code) {
                    throw new Error(`الصف ${i + 1}: اسم الصنف والكود ورقم الصنف المخزني مطلوبان`);
                }

                // البحث عن الصنف الموجود بنفس الكود أو رقم الصنف المخزني
                const existingItem = await this.findItemByCodeOrItemCode(item.code, item.itemCode);

                if (existingItem) {
                    // التحقق من تطابق الاسم أيضاً لتجنب الكتابة فوق أصناف مختلفة
                    const isSameItem = (
                        existingItem.name.trim().toLowerCase() === item.name.trim().toLowerCase() ||
                        (existingItem.itemCode === item.itemCode && existingItem.code === item.code)
                    );

                    if (isSameItem) {
                        // تحديث الصنف الموجود
                        console.log(`🔄 تحديث صنف موجود: ${existingItem.name} (ID: ${existingItem.id})`);
                        console.log(`   - الكود الموجود: "${existingItem.itemCode}" vs الجديد: "${item.itemCode}"`);
                        console.log(`   - الرقم المخزني الموجود: "${existingItem.code}" vs الجديد: "${item.code}"`);

                        const updatedItem = {
                            ...existingItem,
                            ...item,
                            id: existingItem.id, // الحفاظ على المعرف الأصلي
                            createdAt: existingItem.createdAt // الحفاظ على تاريخ الإنشاء الأصلي
                        };

                        await this.updateItem(existingItem.id, updatedItem);
                        updatedCount++;
                        console.log(`✅ تم تحديث الصنف: ${item.name} (${item.itemCode})`);
                    } else {
                        // صنف مختلف بنفس الكود - إضافة كصنف جديد مع تعديل الكود
                        console.log(`⚠️ تضارب في الكود! صنف موجود: "${existingItem.name}" vs جديد: "${item.name}"`);
                        console.log(`   سيتم إضافة الصنف الجديد مع تعديل الكود`);

                        // إنشاء كود فريد
                        item.itemCode = `${item.itemCode}_${Date.now()}`;
                        item.code = `${item.code}_${Date.now()}`;
                        item.createdAt = new Date().toISOString();

                        await this.addItem(item);
                        successCount++;
                        console.log(`✅ تم إضافة صنف جديد مع كود معدل: ${item.name} (${item.itemCode})`);
                    }
                } else {
                    // إضافة صنف جديد
                    console.log(`➕ إضافة صنف جديد: ${item.name} (${item.itemCode})`);
                    item.createdAt = new Date().toISOString();
                    await this.addItem(item);
                    successCount++;
                    console.log(`✅ تم إضافة صنف جديد: ${item.name} (${item.itemCode})`);
                }

            } catch (error) {
                errorCount++;
                errors.push(`الصف ${i + 1}: ${error.message}`);
                console.error(`❌ خطأ في الصف ${i + 1}:`, error);
            }
        }

        console.log(`📊 نتائج الاستيراد: ${successCount} جديد، ${updatedCount} محدث، ${errorCount} خطأ`);

        return {
            success: true,
            successCount: successCount,
            updatedCount: updatedCount,
            errorCount: errorCount,
            errorDetails: errors
        };
    }

    // البحث عن صنف بالكود أو رقم الصنف المخزني
    async findItemByCodeOrItemCode(code, itemCode) {
        return new Promise((resolve, reject) => {
            console.log(`🔍 البحث عن صنف: كود="${code}", رقم مخزني="${itemCode}"`);

            const transaction = this.db.transaction(['items'], 'readonly');
            const store = transaction.objectStore('items');

            // البحث بالكود أولاً
            const codeIndex = store.index('code');
            const codeRequest = codeIndex.get(code);

            codeRequest.onsuccess = () => {
                if (codeRequest.result) {
                    console.log(`✅ تم العثور على صنف بالكود "${code}": ${codeRequest.result.name}`);
                    resolve(codeRequest.result);
                    return;
                }

                console.log(`❌ لم يتم العثور على صنف بالكود "${code}"`);

                // إذا لم يوجد بالكود، ابحث برقم الصنف المخزني
                const itemCodeIndex = store.index('itemCode');
                const itemCodeRequest = itemCodeIndex.get(itemCode);

                itemCodeRequest.onsuccess = () => {
                    if (itemCodeRequest.result) {
                        console.log(`✅ تم العثور على صنف برقم الصنف المخزني "${itemCode}": ${itemCodeRequest.result.name}`);
                    } else {
                        console.log(`❌ لم يتم العثور على صنف برقم الصنف المخزني "${itemCode}"`);
                    }
                    resolve(itemCodeRequest.result || null);
                };

                itemCodeRequest.onerror = () => {
                    console.error(`❌ خطأ في البحث برقم الصنف المخزني "${itemCode}":`, itemCodeRequest.error);
                    reject(itemCodeRequest.error);
                };
            };

            codeRequest.onerror = () => {
                console.error(`❌ خطأ في البحث بالكود "${code}":`, codeRequest.error);
                reject(codeRequest.error);
            };
        });
    }

    // مسح جميع البيانات
    async clearAllData() {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['items'], 'readwrite');
            const store = transaction.objectStore('items');

            const request = store.clear();

            request.onsuccess = () => {
                resolve(true);
            };

            request.onerror = () => {
                reject(request.error);
            };
        });
    }

    // دوال إدارة بيان الصرف
    async addDispensingUpdate(updateData) {
        return new Promise((resolve, reject) => {
            try {
                // التحقق من وجود قاعدة البيانات
                if (!this.db) {
                    reject(new Error('قاعدة البيانات غير متاحة'));
                    return;
                }

                // التحقق من وجود الجدول
                if (!this.db.objectStoreNames.contains('dispensingUpdates')) {
                    reject(new Error('جدول dispensingUpdates غير موجود'));
                    return;
                }

                const transaction = this.db.transaction(['dispensingUpdates'], 'readwrite');
                const store = transaction.objectStore('dispensingUpdates');

                const update = {
                    ...updateData,
                    id: Date.now().toString() + Math.floor(Math.random() * 1000).toString(),
                    createdAt: new Date().toISOString()
                };

                console.log('إضافة تحديث بيان الصرف:', update);

                const request = store.add(update);

                request.onsuccess = () => {
                    console.log('تم حفظ التحديث بنجاح');
                    resolve(update);
                };

                request.onerror = () => {
                    console.error('خطأ في حفظ التحديث:', request.error);
                    reject(request.error);
                };

            } catch (error) {
                console.error('خطأ في addDispensingUpdate:', error);
                reject(error);
            }
        });
    }

    async getDispensingUpdates(itemId) {
        return new Promise((resolve, reject) => {
            try {
                // التحقق من وجود قاعدة البيانات
                if (!this.db) {
                    console.log('قاعدة البيانات غير متاحة في getDispensingUpdates');
                    resolve([]);
                    return;
                }

                // التحقق من وجود الجدول
                if (!this.db.objectStoreNames.contains('dispensingUpdates')) {
                    console.log('جدول dispensingUpdates غير موجود');
                    resolve([]);
                    return;
                }

                const transaction = this.db.transaction(['dispensingUpdates'], 'readonly');
                const store = transaction.objectStore('dispensingUpdates');
                const index = store.index('itemId');

                const request = index.getAll(itemId);

                request.onsuccess = () => {
                    const updates = request.result || [];
                    // ترتيب التحديثات حسب التاريخ (الأحدث أولاً)
                    updates.sort((a, b) => new Date(b.date) - new Date(a.date));
                    console.log(`تم العثور على ${updates.length} تحديث للصنف ${itemId}`);
                    resolve(updates);
                };

                request.onerror = () => {
                    console.error('خطأ في استرجاع تحديثات بيان الصرف:', request.error);
                    resolve([]); // إرجاع مصفوفة فارغة بدلاً من رفض
                };

            } catch (error) {
                console.error('خطأ في getDispensingUpdates:', error);
                resolve([]);
            }
        });
    }

    async deleteDispensingUpdate(updateId) {
        return new Promise((resolve, reject) => {
            try {
                // التحقق من وجود قاعدة البيانات
                if (!this.db) {
                    reject(new Error('قاعدة البيانات غير متاحة'));
                    return;
                }

                // التحقق من وجود الجدول
                if (!this.db.objectStoreNames.contains('dispensingUpdates')) {
                    reject(new Error('جدول dispensingUpdates غير موجود'));
                    return;
                }

                console.log('حذف التحديث من قاعدة البيانات:', updateId, 'نوع البيانات:', typeof updateId);

                // أولاً، اعرض جميع التحديثات الموجودة للتشخيص
                this.debugAllUpdates();

                const transaction = this.db.transaction(['dispensingUpdates'], 'readwrite');
                const store = transaction.objectStore('dispensingUpdates');

                // تحويل المعرف إلى النوع الصحيح إذا لزم الأمر
                let keyToDelete = updateId;

                // جرب أنواع مختلفة من المفاتيح
                console.log('المفتاح الأصلي:', updateId, 'نوعه:', typeof updateId);

                // أولاً، جرب المفتاح كما هو
                let getRequest = store.get(keyToDelete);

                getRequest.onsuccess = () => {
                    if (getRequest.result) {
                        console.log('تم العثور على التحديث بالمفتاح الأصلي');
                        this.performDelete(store, keyToDelete, resolve, reject);
                    } else {
                        // جرب تحويل إلى نص
                        keyToDelete = updateId.toString();
                        console.log('جرب المفتاح كنص:', keyToDelete);

                        const getRequest2 = store.get(keyToDelete);
                        getRequest2.onsuccess = () => {
                            if (getRequest2.result) {
                                console.log('تم العثور على التحديث بالمفتاح النصي');
                                this.performDelete(store, keyToDelete, resolve, reject);
                            } else {
                                // جرب تحويل إلى رقم
                                keyToDelete = parseFloat(updateId);
                                console.log('جرب المفتاح كرقم:', keyToDelete);

                                const getRequest3 = store.get(keyToDelete);
                                getRequest3.onsuccess = () => {
                                    if (getRequest3.result) {
                                        console.log('تم العثور على التحديث بالمفتاح الرقمي');
                                        this.performDelete(store, keyToDelete, resolve, reject);
                                    } else {
                                        // كحل أخير، جرب البحث في جميع التحديثات
                                        console.log('البحث في جميع التحديثات...');
                                        const getAllRequest = store.getAll();
                                        getAllRequest.onsuccess = () => {
                                            const allUpdates = getAllRequest.result || [];
                                            const foundUpdate = allUpdates.find(u =>
                                                u.id == updateId ||
                                                u.id === updateId ||
                                                u.id.toString() === updateId.toString()
                                            );

                                            if (foundUpdate) {
                                                console.log('تم العثور على التحديث في البحث الشامل:', foundUpdate.id);
                                                this.performDelete(store, foundUpdate.id, resolve, reject);
                                            } else {
                                                console.error('لم يتم العثور على التحديث بأي من الطرق');
                                                console.log('المعرف المطلوب:', updateId);
                                                console.log('المعرفات الموجودة:', allUpdates.map(u => u.id));
                                                reject(new Error('التحديث غير موجود'));
                                            }
                                        };
                                        getAllRequest.onerror = () => reject(getAllRequest.error);
                                    }
                                };
                                getRequest3.onerror = () => reject(getRequest3.error);
                            }
                        };
                        getRequest2.onerror = () => reject(getRequest2.error);
                    }
                };

                getRequest.onerror = () => {
                    console.error('خطأ في البحث عن التحديث:', getRequest.error);
                    reject(getRequest.error);
                };

            } catch (error) {
                console.error('خطأ في deleteDispensingUpdate:', error);
                reject(error);
            }
        });
    }

    // دالة مساعدة لتنفيذ الحذف
    performDelete(store, keyToDelete, resolve, reject) {
        console.log('تنفيذ الحذف للمفتاح:', keyToDelete);
        const deleteRequest = store.delete(keyToDelete);

        deleteRequest.onsuccess = () => {
            console.log('تم حذف التحديث بنجاح من قاعدة البيانات');
            resolve();
        };

        deleteRequest.onerror = () => {
            console.error('خطأ في حذف التحديث من قاعدة البيانات:', deleteRequest.error);
            reject(deleteRequest.error);
        };
    }

    // دالة للتشخيص - عرض جميع التحديثات
    debugAllUpdates() {
        try {
            const transaction = this.db.transaction(['dispensingUpdates'], 'readonly');
            const store = transaction.objectStore('dispensingUpdates');
            const request = store.getAll();

            request.onsuccess = () => {
                const allUpdates = request.result || [];
                console.log('جميع التحديثات في قاعدة البيانات:');
                allUpdates.forEach((update, index) => {
                    console.log(`${index + 1}. ID: ${update.id} (${typeof update.id}), Type: ${update.type}, Date: ${update.date}`);
                });
            };

            request.onerror = () => {
                console.error('خطأ في استرجاع التحديثات للتشخيص');
            };
        } catch (error) {
            console.error('خطأ في debugAllUpdates:', error);
        }
    }

    async getAllDispensingUpdates() {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['dispensingUpdates'], 'readonly');
            const store = transaction.objectStore('dispensingUpdates');

            const request = store.getAll();

            request.onsuccess = () => {
                const updates = request.result || [];
                updates.sort((a, b) => new Date(b.date) - new Date(a.date));
                resolve(updates);
            };
            request.onerror = () => reject(request.error);
        });
    }
}

// إنشاء مثيل من مدير قاعدة البيانات
const dbManager = new DatabaseManager();
