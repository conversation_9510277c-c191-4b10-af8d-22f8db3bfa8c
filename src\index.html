<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>شركة الصناعات الكيماوية المصرية - كيما</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- خطوط عربية -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- أيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- ملف التنسيقات المخصص -->
    <link rel="stylesheet" href="styles.css">

    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        'kima-green': '#2d5016',
                        'kima-light-green': '#4a7c23',
                        'kima-bg': '#f8fafc',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-cairo bg-kima-bg min-h-screen" dir="rtl">
    <!-- الهيدر -->
    <header class="bg-gradient-to-r from-kima-green to-kima-light-green text-white shadow-lg">
        <div class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4 space-x-reverse">
                    <div>
                        <h1 class="text-2xl font-bold">شركة الصناعات الكيماوية المصرية - كيما</h1>
                        <p class="text-green-100 text-sm">نظام إدارة المخازن</p>
                    </div>
                    <img src="../assets/kima-logo-white.svg" alt="شعار كيما" class="header-logo w-12 h-12 object-contain">
                </div>
                <div class="flex items-center space-x-4 space-x-reverse">
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <span class="text-sm opacity-75" id="currentTime"></span>
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <span class="text-sm opacity-75">أسوان</span>
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <span class="text-sm opacity-75" id="currentDate"></span>
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- المحتوى الرئيسي -->
    <main class="container mx-auto px-6 py-8">
        <!-- شريط البحث والأزرار -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex flex-col lg:flex-row gap-4 items-center justify-between">
                <!-- حقل البحث -->
                <div class="flex-1 w-full lg:w-auto">
                    <div class="relative">
                        <input
                            type="text"
                            id="searchInput"
                            placeholder="ابحث في اسم الصنف أو الكود..."
                            class="w-full px-4 py-3 pr-12 pl-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kima-green focus:border-transparent transition-all duration-200"
                        >
                        <i class="fas fa-search absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        <button
                            id="advancedSearchBtn"
                            class="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-kima-green transition-colors p-2"
                            title="بحث متقدم"
                        >
                            <i class="fas fa-filter"></i>
                        </button>
                    </div>
                </div>

                <!-- الأزرار -->
                <div class="flex flex-wrap gap-3">
                    <button
                        id="addItemBtn"
                        class="bg-kima-green hover:bg-kima-light-green text-white px-6 py-3 rounded-lg transition-all duration-200 flex items-center space-x-2 space-x-reverse shadow-md hover:shadow-lg"
                    >
                        <i class="fas fa-plus"></i>
                        <span>إضافة صنف جديد</span>
                    </button>



                    <button
                        id="resetSearchBtn"
                        class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg transition-all duration-200 flex items-center space-x-2 space-x-reverse shadow-md hover:shadow-lg hidden"
                        title="إعادة تعيين البحث"
                    >
                        <i class="fas fa-undo"></i>
                        <span>إعادة تعيين</span>
                    </button>


                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between">
                    <div class="text-gray-600 text-lg font-medium">إجمالي الأصناف</div>
                    <div class="text-3xl font-bold text-kima-green" id="totalItems">0</div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between">
                    <div class="text-gray-600 text-lg font-medium">الموردين</div>
                    <div class="text-3xl font-bold text-orange-600" id="totalSuppliers">0</div>
                </div>
            </div>
        </div>

        <!-- جدول البيانات -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-4 text-center text-sm font-semibold text-gray-700">م</th>
                            <th class="px-6 py-4 text-right text-sm font-semibold text-gray-700">اسم الصنف / الرقم</th>
                            <th class="px-6 py-4 text-right text-sm font-semibold text-gray-700">الكود</th>
                            <th class="px-6 py-4 text-right text-sm font-semibold text-gray-700">المواصفات</th>
                            <th class="px-6 py-4 text-right text-sm font-semibold text-gray-700">رقم الصنف المخزني</th>
                            <th class="px-6 py-4 text-right text-sm font-semibold text-gray-700">الرصيد</th>
                            <th class="px-6 py-4 text-right text-sm font-semibold text-gray-700">رقم الشلف</th>
                            <th class="px-6 py-4 text-right text-sm font-semibold text-gray-700">رقم العين</th>
                            <th class="px-6 py-4 text-right text-sm font-semibold text-gray-700">اسم المورد</th>
                            <th class="px-6 py-4 text-right text-sm font-semibold text-gray-700">تاريخ التوريد</th>
                            <th class="px-6 py-4 text-center text-sm font-semibold text-gray-700">بيان الصرف</th>
                        </tr>
                    </thead>
                    <tbody id="itemsTableBody" class="divide-y divide-gray-200">
                        <!-- سيتم ملء البيانات هنا بواسطة JavaScript -->
                    </tbody>
                </table>
            </div>

            <!-- رسالة عدم وجود بيانات -->
            <div id="noDataMessage" class="text-center py-12 hidden">
                <i class="fas fa-box-open text-6xl text-gray-300 mb-4"></i>
                <p class="text-gray-500 text-lg">لا توجد أصناف مسجلة</p>
                <p class="text-gray-400 text-sm">ابدأ بإضافة أول صنف</p>
            </div>
        </div>
    </main>

    <!-- نافذة إضافة/تعديل صنف -->
    <div id="itemModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <h2 id="modalTitle" class="text-xl font-bold text-gray-800">إضافة صنف جديد</h2>
                    <button id="closeModal" class="text-gray-400 hover:text-gray-600 text-2xl">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <form id="itemForm" class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">اسم الصنف / الرقم *</label>
                            <input type="text" id="itemName" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kima-green focus:border-transparent">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الكود *</label>
                            <input type="text" id="itemCodeField" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kima-green focus:border-transparent">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">رقم الصنف المخزني *</label>
                            <input type="text" id="itemCode" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kima-green focus:border-transparent">
                        </div>

                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">المواصفات</label>
                            <textarea id="itemSpecs" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kima-green focus:border-transparent"></textarea>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الرصيد *</label>
                            <input type="number" id="itemStock" required min="0" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kima-green focus:border-transparent">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">رقم الشلف</label>
                            <input type="text" id="shelfNumber" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kima-green focus:border-transparent">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">رقم العين</label>
                            <input type="text" id="eyeNumber" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kima-green focus:border-transparent">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">اسم المورد</label>
                            <input type="text" id="supplierName" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kima-green focus:border-transparent">
                        </div>

                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">بيان الصرف</label>
                            <textarea id="dispensingInfo" rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kima-green focus:border-transparent"></textarea>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ التوريد</label>
                            <input type="date" id="supplyDate" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kima-green focus:border-transparent">
                        </div>
                    </div>

                    <div class="flex justify-end space-x-3 space-x-reverse pt-6">
                        <button type="button" id="cancelBtn" class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                            إلغاء
                        </button>
                        <button type="submit" class="px-6 py-2 bg-kima-green text-white rounded-lg hover:bg-kima-light-green transition-colors">
                            حفظ
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة البحث المتقدم -->
    <div id="advancedSearchModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-bold text-gray-800">البحث المتقدم</h2>
                    <button id="closeAdvancedSearch" class="text-gray-400 hover:text-gray-600 text-2xl">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <form id="advancedSearchForm" class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <!-- البحث في اسم الصنف -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">اسم الصنف</label>
                            <input type="text" id="searchName" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kima-green focus:border-transparent">
                        </div>

                        <!-- البحث في الكود -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الكود</label>
                            <input type="text" id="searchCode" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kima-green focus:border-transparent">
                        </div>

                        <!-- البحث في رقم الصنف المخزني -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">رقم الصنف المخزني</label>
                            <input type="text" id="searchItemCode" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kima-green focus:border-transparent">
                        </div>

                        <!-- البحث في المورد -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">اسم المورد</label>
                            <select id="searchSupplier" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kima-green focus:border-transparent">
                                <option value="">جميع الموردين</option>
                            </select>
                        </div>

                        <!-- البحث في رقم الشلف -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">رقم الشلف</label>
                            <input type="text" id="searchShelf" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kima-green focus:border-transparent">
                        </div>

                        <!-- البحث في رقم العين -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">رقم العين</label>
                            <input type="text" id="searchEye" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kima-green focus:border-transparent">
                        </div>

                        <!-- نطاق الرصيد -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الرصيد من</label>
                            <input type="number" id="searchStockMin" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kima-green focus:border-transparent">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الرصيد إلى</label>
                            <input type="number" id="searchStockMax" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kima-green focus:border-transparent">
                        </div>

                        <!-- تاريخ التوريد -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ التوريد من</label>
                            <input type="date" id="searchDateFrom" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kima-green focus:border-transparent">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ التوريد إلى</label>
                            <input type="date" id="searchDateTo" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kima-green focus:border-transparent">
                        </div>

                        <!-- فلتر حالة المخزون -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">حالة المخزون</label>
                            <select id="searchStockStatus" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kima-green focus:border-transparent">
                                <option value="">جميع الحالات</option>
                                <option value="available">متوفر (أكثر من 10)</option>
                                <option value="low">منخفض (1-10)</option>
                                <option value="empty">نفد المخزون (0)</option>
                            </select>
                        </div>
                    </div>

                    <!-- البحث في المواصفات وبيان الصرف -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">المواصفات</label>
                            <textarea id="searchSpecs" rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kima-green focus:border-transparent"></textarea>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">بيان الصرف</label>
                            <textarea id="searchDispensing" rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kima-green focus:border-transparent"></textarea>
                        </div>
                    </div>

                    <!-- أزرار التحكم -->
                    <div class="flex justify-between items-center pt-6 border-t">
                        <div class="flex space-x-3 space-x-reverse">
                            <button type="button" id="clearAdvancedSearch" class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors">
                                <i class="fas fa-eraser ml-2"></i>
                                مسح الكل
                            </button>
                            <button type="button" id="saveSearchTemplate" class="px-4 py-2 text-blue-600 hover:text-blue-800 transition-colors">
                                <i class="fas fa-save ml-2"></i>
                                حفظ كقالب
                            </button>
                        </div>

                        <div class="flex space-x-3 space-x-reverse">
                            <button type="button" id="cancelAdvancedSearch" class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                                إلغاء
                            </button>
                            <button type="submit" class="px-6 py-2 bg-kima-green text-white rounded-lg hover:bg-kima-light-green transition-colors">
                                <i class="fas fa-search ml-2"></i>
                                بحث
                            </button>
                        </div>
                    </div>
                </form>

                <!-- نتائج البحث -->
                <div id="searchResults" class="mt-6 hidden">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-800">نتائج البحث</h3>
                        <span id="searchResultsCount" class="text-sm text-gray-600"></span>
                    </div>
                    <div class="bg-gray-50 rounded-lg p-4 max-h-60 overflow-y-auto">
                        <div id="searchResultsList"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة السياق (Right Click Menu) -->
    <div id="contextMenu" class="fixed bg-white border border-gray-200 rounded-lg shadow-lg py-2 z-50 hidden">
        <button id="editContextBtn" class="w-full px-4 py-2 text-right text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2 space-x-reverse">
            <i class="fas fa-edit text-blue-600"></i>
            <span>تعديل</span>
        </button>
        <button id="deleteContextBtn" class="w-full px-4 py-2 text-right text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2 space-x-reverse">
            <i class="fas fa-trash"></i>
            <span>حذف</span>
        </button>
    </div>

    <!-- حاوي الإشعارات -->
    <div id="notificationContainer"></div>

    <!-- نافذة بيان الصرف -->
    <div id="dispensingModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-3xl max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-bold text-gray-800">
                        <i class="fas fa-clipboard-list text-kima-green ml-2"></i>
                        بيان الصرف - <span id="dispensingItemName" class="text-kima-green"></span>
                    </h2>
                    <button id="closeDispensingModal" class="text-gray-400 hover:text-gray-600 text-2xl">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <!-- إضافة تحديث جديد -->
                <div class="bg-gray-50 rounded-lg p-4 mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">
                        <i class="fas fa-plus-circle text-blue-600 ml-2"></i>
                        إضافة تحديث جديد
                    </h3>
                    <form id="dispensingUpdateForm" class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">نوع العملية</label>
                                <select id="dispensingType" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kima-green focus:border-transparent">
                                    <option value="صرف">صرف</option>
                                    <option value="إضافة">إضافة</option>
                                    <option value="جرد">جرد</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">الكمية</label>
                                <input type="number" id="dispensingQuantity" min="0" step="0.01" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kima-green focus:border-transparent" required>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">المسؤول</label>
                                <input type="text" id="dispensingResponsible" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kima-green focus:border-transparent" required>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">التاريخ</label>
                                <input type="datetime-local" id="dispensingDate" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kima-green focus:border-transparent" required>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">ملاحظات</label>
                            <textarea id="dispensingNotes" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kima-green focus:border-transparent" placeholder="أدخل تفاصيل العملية..."></textarea>
                        </div>

                        <div class="flex justify-end space-x-3 space-x-reverse">
                            <button type="button" id="cancelDispensingUpdate" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                                إلغاء
                            </button>
                            <button type="submit" class="px-6 py-2 bg-kima-green text-white rounded-lg hover:bg-kima-light-green transition-colors">
                                <i class="fas fa-save ml-2"></i>
                                حفظ التحديث
                            </button>
                        </div>
                    </form>
                </div>

                <!-- تاريخ التحديثات -->
                <div>
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-800">
                            <i class="fas fa-history text-orange-600 ml-2"></i>
                            تاريخ التحديثات
                        </h3>
                        <span id="dispensingUpdatesCount" class="text-sm text-gray-600"></span>
                    </div>

                    <div id="dispensingUpdatesList" class="space-y-3 max-h-96 overflow-y-auto">
                        <!-- سيتم ملء التحديثات هنا -->
                    </div>

                    <!-- رسالة عدم وجود تحديثات -->
                    <div id="noDispensingUpdates" class="text-center py-8 hidden">
                        <i class="fas fa-clipboard text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500">لا توجد تحديثات لبيان الصرف</p>
                        <p class="text-gray-400 text-sm">ابدأ بإضافة أول تحديث</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل ملفات JavaScript -->
    <script src="database.js"></script>
    <script src="backup.js"></script>
    <script src="renderer.js"></script>
</body>
</html>
