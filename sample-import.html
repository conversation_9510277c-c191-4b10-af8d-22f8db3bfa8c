<!DOCTYPE html>
<html>
<head>
    <title>إنشاء ملف Excel نموذجي للاستيراد</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2D5016;
            text-align: center;
            margin-bottom: 30px;
        }
        .info {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-right: 4px solid #2D5016;
        }
        button {
            background: #2D5016;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #1a3009;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        th {
            background-color: #2D5016;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>إنشاء ملف Excel نموذجي للاستيراد</h1>
        
        <div class="info">
            <h3>تنسيق البيانات المطلوب:</h3>
            <p>يجب أن يحتوي ملف Excel على الأعمدة التالية بنفس الترتيب:</p>
            <ol>
                <li><strong>اسم الصنف</strong> - مطلوب</li>
                <li><strong>الكود</strong> - مطلوب</li>
                <li><strong>المواصفات</strong> - اختياري</li>
                <li><strong>رقم الصنف المخزني</strong> - مطلوب</li>
                <li><strong>الرصيد</strong> - رقم</li>
                <li><strong>رقم الشلف</strong> - اختياري</li>
                <li><strong>رقم العين</strong> - اختياري</li>
                <li><strong>اسم المورد</strong> - اختياري</li>
                <li><strong>تاريخ التوريد</strong> - اختياري</li>
            </ol>
            <p><strong>ملاحظة مهمة:</strong> سيتم تجاهل الصف الأول تلقائياً إذا كان يحتوي على عناوين الأعمدة.</p>
        </div>

        <button onclick="createSampleFile()">إنشاء ملف Excel نموذجي</button>
        <button onclick="createTemplateFile()">إنشاء قالب فارغ</button>

        <h3>مثال على البيانات:</h3>
        <table id="sampleTable">
            <thead>
                <tr>
                    <th>اسم الصنف</th>
                    <th>الكود</th>
                    <th>المواصفات</th>
                    <th>رقم الصنف المخزني</th>
                    <th>الرصيد</th>
                    <th>رقم الشلف</th>
                    <th>رقم العين</th>
                    <th>اسم المورد</th>
                    <th>تاريخ التوريد</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>مضخة مياه</td>
                    <td>PUMP001</td>
                    <td>مضخة مياه 5 حصان</td>
                    <td>WP001</td>
                    <td>10</td>
                    <td>A1</td>
                    <td>01</td>
                    <td>شركة المضخات المصرية</td>
                    <td>2024-01-15</td>
                </tr>
                <tr>
                    <td>صمام أمان</td>
                    <td>VALVE002</td>
                    <td>صمام أمان 2 بوصة</td>
                    <td>SV002</td>
                    <td>25</td>
                    <td>B2</td>
                    <td>03</td>
                    <td>شركة الصمامات الحديثة</td>
                    <td>2024-02-10</td>
                </tr>
                <tr>
                    <td>محرك كهربائي</td>
                    <td>MOTOR003</td>
                    <td>محرك 3 فاز 10 حصان</td>
                    <td>EM003</td>
                    <td>5</td>
                    <td>C1</td>
                    <td>02</td>
                    <td>الشركة العربية للمحركات</td>
                    <td>2024-03-05</td>
                </tr>
            </tbody>
        </table>
    </div>

    <script>
        function createSampleFile() {
            const data = [
                ['اسم الصنف', 'الكود', 'المواصفات', 'رقم الصنف المخزني', 'الرصيد', 'رقم الشلف', 'رقم العين', 'اسم المورد', 'تاريخ التوريد'],
                ['مضخة مياه', 'PUMP001', 'مضخة مياه 5 حصان', 'WP001', 10, 'A1', '01', 'شركة المضخات المصرية', '2024-01-15'],
                ['صمام أمان', 'VALVE002', 'صمام أمان 2 بوصة', 'SV002', 25, 'B2', '03', 'شركة الصمامات الحديثة', '2024-02-10'],
                ['محرك كهربائي', 'MOTOR003', 'محرك 3 فاز 10 حصان', 'EM003', 5, 'C1', '02', 'الشركة العربية للمحركات', '2024-03-05'],
                ['مرشح هواء', 'FILTER004', 'مرشح هواء عالي الكفاءة', 'AF004', 50, 'D3', '05', 'شركة المرشحات المتقدمة', '2024-01-20'],
                ['حساس حرارة', 'SENSOR005', 'حساس حرارة رقمي', 'TS005', 15, 'E2', '04', 'شركة الحساسات الذكية', '2024-02-25'],
                ['مضخة ضغط', 'PUMP006', 'مضخة ضغط عالي', 'HP006', 100, 'F1', '06', 'شركة المضخات الصناعية', '15/01/2024'],
                ['صمام تحكم', 'VALVE007', 'صمام تحكم إلكتروني', 'CV007', 75, 'G2', '07', 'شركة التحكم الآلي', '10-02-2024']
            ];

            createExcelFile(data, 'عينة-بيانات-للاستيراد.xlsx');
        }

        function createTemplateFile() {
            const data = [
                ['اسم الصنف', 'الكود', 'المواصفات', 'رقم الصنف المخزني', 'الرصيد', 'رقم الشلف', 'رقم العين', 'اسم المورد', 'تاريخ التوريد']
            ];

            createExcelFile(data, 'قالب-استيراد-فارغ.xlsx');
        }

        function createExcelFile(data, filename) {
            const worksheet = XLSX.utils.aoa_to_sheet(data);
            
            // تحسين عرض الأعمدة
            const columnWidths = [
                { wch: 25 }, // اسم الصنف
                { wch: 15 }, // الكود
                { wch: 30 }, // المواصفات
                { wch: 18 }, // رقم الصنف المخزني
                { wch: 10 }, // الرصيد
                { wch: 12 }, // رقم الشلف
                { wch: 12 }, // رقم العين
                { wch: 25 }, // اسم المورد
                { wch: 15 }  // تاريخ التوريد
            ];
            worksheet['!cols'] = columnWidths;

            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, 'البيانات');

            XLSX.writeFile(workbook, filename);
            
            alert('تم إنشاء الملف بنجاح: ' + filename);
        }
    </script>
</body>
</html>
