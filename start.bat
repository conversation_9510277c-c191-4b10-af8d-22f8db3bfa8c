@echo off
echo ========================================
echo   تطبيق مخازن كيما - بدء التشغيل
echo ========================================
echo.

REM التحقق من وجود Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Node.js غير مثبت على النظام
    echo يرجى تحميل وتثبيت Node.js من: https://nodejs.org/
    pause
    exit /b 1
)

echo تم العثور على Node.js...

REM التحقق من وجود node_modules
if not exist "node_modules" (
    echo تثبيت التبعيات...
    npm install
    if %errorlevel% neq 0 (
        echo خطأ في تثبيت التبعيات
        pause
        exit /b 1
    )
)

echo التحقق من electron...

REM التحقق من وجود electron محلياً
if exist "node_modules\electron\dist\electron.exe" (
    echo تم العثور على electron محلياً
    echo بدء تشغيل التطبيق...
    echo.
    echo ملاحظة: لإغلاق التطبيق، أغلق النافذة أو اضغط Ctrl+C هنا
    echo.
    node_modules\electron\dist\electron.exe . --dev
) else (
    echo electron غير موجود محلياً، جاري التثبيت...
    npm install electron --save-dev
    if %errorlevel% neq 0 (
        echo خطأ في تثبيت electron
        echo جاري المحاولة بـ npm start...
        npm start
    ) else (
        echo تم تثبيت electron، بدء التشغيل...
        node_modules\electron\dist\electron.exe . --dev
    )
)

pause
