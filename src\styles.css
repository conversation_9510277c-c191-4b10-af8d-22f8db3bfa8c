/* تنسيقات مخصصة لتطبيق كيما */

/* إعدادات عامة للغة العربية */
* {
    font-family: 'Cairo', sans-serif;
}

body {
    direction: rtl;
    text-align: right;
}

/* تحسين شكل الجداول */
table {
    border-collapse: separate;
    border-spacing: 0;
}

/* تنسيق خاص للأزرار */
.btn-action {
    @apply px-3 py-1 rounded text-sm font-medium transition-all duration-200;
}

.btn-edit {
    @apply bg-blue-100 text-blue-700 hover:bg-blue-200;
}

.btn-delete {
    @apply bg-red-100 text-red-700 hover:bg-red-200;
}

/* تأثيرات الحركة */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تنسيق خاص للبحث */
#searchInput:focus {
    box-shadow: 0 0 0 3px rgba(45, 80, 22, 0.1);
}

/* تنسيق الجدول المتجاوب */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }

    .table-responsive th,
    .table-responsive td {
        padding: 0.5rem;
    }
}

/* تنسيق النافذة المنبثقة */
.modal-backdrop {
    backdrop-filter: blur(4px);
}

/* جعل نافذة إضافة/تعديل الصنف قابلة للسحب */
.draggable-item-modal {
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    margin: 0 !important;
}

.item-modal-header {
    cursor: move;
    user-select: none;
    background: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
    border-radius: 12px 12px 0 0;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.item-modal-header:hover {
    background: #f3f4f6;
}

.item-modal-header h3 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: #374151;
}

/* تنسيق شريط التمرير */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* تنسيق خاص للإحصائيات */
.stat-card {
    transition: transform 0.2s ease-in-out;
}

.stat-card:hover {
    transform: translateY(-2px);
}

/* تنسيق خاص للرسائل */
.alert {
    @apply px-4 py-3 rounded-lg mb-4;
}

.alert-success {
    @apply bg-green-100 border border-green-400 text-green-700;
}

.alert-error {
    @apply bg-red-100 border border-red-400 text-red-700;
}

.alert-warning {
    @apply bg-yellow-100 border border-yellow-400 text-yellow-700;
}

.alert-info {
    @apply bg-blue-100 border border-blue-400 text-blue-700;
}

/* تنسيق خاص للتحميل */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* تنسيق خاص للحقول المطلوبة */
.required::after {
    content: " *";
    color: #ef4444;
}

/* تحسين شكل النماذج */
.form-group {
    @apply mb-4;
}

.form-label {
    @apply block text-sm font-medium text-gray-700 mb-2;
}

.form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kima-green focus:border-transparent transition-all duration-200;
}

.form-input:focus {
    box-shadow: 0 0 0 3px rgba(45, 80, 22, 0.1);
}

/* تنسيق خاص للحالات الفارغة */
.empty-state {
    @apply text-center py-12;
}

.empty-state-icon {
    @apply text-6xl text-gray-300 mb-4;
}

.empty-state-title {
    @apply text-gray-500 text-lg mb-2;
}

.empty-state-description {
    @apply text-gray-400 text-sm;
}

/* تنسيق خاص للتنبيهات المحسن */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    max-width: 400px;
    min-width: 300px;
    padding: 16px 20px;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: slideInRight 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    font-family: 'Cairo', sans-serif;
    direction: rtl;
}

.notification::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    border-radius: 0 12px 12px 0;
}

/* أنواع الإشعارات المختلفة */
.notification.success {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.95), rgba(22, 163, 74, 0.95));
    color: white;
    border-color: rgba(34, 197, 94, 0.3);
}

.notification.success::before {
    background: rgba(255, 255, 255, 0.8);
}

.notification.error {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.95), rgba(220, 38, 38, 0.95));
    color: white;
    border-color: rgba(239, 68, 68, 0.3);
}

.notification.error::before {
    background: rgba(255, 255, 255, 0.8);
}

.notification.warning {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.95), rgba(217, 119, 6, 0.95));
    color: white;
    border-color: rgba(245, 158, 11, 0.3);
}

.notification.warning::before {
    background: rgba(255, 255, 255, 0.8);
}

.notification.info {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.95), rgba(37, 99, 235, 0.95));
    color: white;
    border-color: rgba(59, 130, 246, 0.3);
}

.notification.info::before {
    background: rgba(255, 255, 255, 0.8);
}

/* تنسيق محتوى الإشعار */
.notification .notification-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.notification .notification-icon {
    font-size: 20px;
    opacity: 0.9;
    flex-shrink: 0;
}

.notification .notification-message {
    flex: 1;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.4;
}

.notification .notification-close {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    font-size: 16px;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.notification .notification-close:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.notification .notification-action {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    cursor: pointer;
    font-size: 12px;
    padding: 6px 12px;
    border-radius: 6px;
    transition: all 0.2s ease;
    font-weight: 500;
    margin-left: 8px;
}

.notification .notification-action:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
}

/* حاوي الإشعارات */
#notificationContainer {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    pointer-events: none;
}

#notificationContainer .notification {
    position: relative;
    top: auto;
    right: auto;
    margin-bottom: 12px;
    pointer-events: all;
}

/* الحركات والانتقالات */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.notification.removing {
    animation: slideOutRight 0.3s ease-in forwards;
}

/* شريط التقدم */
.notification .progress-bar {
    position: absolute;
    bottom: 0;
    right: 0;
    height: 3px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 0 0 12px 12px;
    animation: progressBar 5s linear forwards;
}

@keyframes progressBar {
    from {
        width: 100%;
    }
    to {
        width: 0%;
    }
}

/* تنسيق قائمة السياق */
#contextMenu {
    min-width: 150px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid #e5e7eb;
    animation: fadeIn 0.15s ease-out;
}

#contextMenu button {
    transition: all 0.2s ease;
    border: none;
    background: none;
    cursor: pointer;
}

#contextMenu button:hover {
    background-color: #f3f4f6;
}

#contextMenu button:active {
    transform: scale(0.98);
}

/* تنسيق الصفوف القابلة للنقر */
tbody tr {
    transition: all 0.2s ease;
}

tbody tr:hover {
    background-color: #f9fafb !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

tbody tr:active {
    transform: translateY(0);
}

/* تنسيق عمود المسلسل */
td:first-child {
    font-weight: 600;
    color: #6b7280;
    background-color: #f9fafb;
}

/* تحسين الطباعة */
@media print {
    .no-print {
        display: none !important;
    }

    #contextMenu {
        display: none !important;
    }

    body {
        font-size: 12px;
        line-height: 1.4;
    }

    .container {
        max-width: none;
        margin: 0;
        padding: 0;
    }

    tbody tr:hover {
        background-color: transparent !important;
        transform: none !important;
        box-shadow: none !important;
    }
}

/* تنسيقات البحث المتقدم محسنة للأداء */
#advancedSearchModal {
    backdrop-filter: blur(2px);
    will-change: opacity;
}

#advancedSearchModal .bg-white {
    animation: modalSlideIn 0.2s ease-out;
    will-change: transform, opacity;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تنسيق حقول البحث المتقدم */
#advancedSearchForm input:focus,
#advancedSearchForm select:focus,
#advancedSearchForm textarea:focus {
    box-shadow: 0 0 0 3px rgba(45, 80, 22, 0.1);
}

/* تنسيق نتائج البحث */
#searchResults {
    animation: fadeIn 0.3s ease-in-out;
}

#searchResultsList .border-b:last-child {
    border-bottom: none !important;
}

/* تحسين زر البحث المتقدم */
#advancedSearchBtn {
    border-radius: 6px;
    transition: all 0.2s ease;
}

#advancedSearchBtn:hover {
    background-color: rgba(45, 80, 22, 0.1);
    transform: scale(1.05);
}

/* تنسيق أزرار النموذج */
#advancedSearchForm button[type="submit"] {
    box-shadow: 0 2px 4px rgba(45, 80, 22, 0.2);
}

#advancedSearchForm button[type="submit"]:hover {
    box-shadow: 0 4px 8px rgba(45, 80, 22, 0.3);
    transform: translateY(-1px);
}

/* تحسين مظهر الحقول - محسن للأداء */
#advancedSearchForm .grid > div {
    transition: transform 0.15s ease;
    will-change: transform;
}

#advancedSearchForm .grid > div:hover {
    transform: translateY(-1px);
}

/* تنسيق خاص للتواريخ */
input[type="date"] {
    direction: ltr;
    text-align: right;
}

/* تحسين مظهر القوائم المنسدلة */
select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: left 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-left: 2.5rem;
}

/* تنسيقات نافذة بيان الصرف */
#dispensingModal {
    backdrop-filter: blur(2px);
    will-change: opacity;
}

#dispensingModal .bg-white {
    animation: modalSlideIn 0.2s ease-out;
    will-change: transform, opacity;
}

/* تنسيق بطاقات التحديثات */
.dispensing-update-card {
    transition: all 0.2s ease;
}

.dispensing-update-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* تنسيق أزرار نوع العملية */
.operation-type-badge {
    font-weight: 600;
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 9999px;
    border-width: 1px;
    transition: all 0.2s ease;
}

/* تنسيق اللوجو الأبيض في الهيدر */
.header-logo {
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
    transition: all 0.3s ease;
}

.header-logo:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15));
}

/* تنسيق زر بيان الصرف في الجدول */
.dispensing-btn {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);
    transition: all 0.2s ease;
}

.dispensing-btn:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
    box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3);
    transform: translateY(-1px);
}

/* تحسين مظهر النماذج */
#dispensingUpdateForm input:focus,
#dispensingUpdateForm select:focus,
#dispensingUpdateForm textarea:focus {
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    border-color: #2563eb;
}

/* تنسيق قائمة التحديثات */
#dispensingUpdatesList {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
}

#dispensingUpdatesList::-webkit-scrollbar {
    width: 6px;
}

#dispensingUpdatesList::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

#dispensingUpdatesList::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

#dispensingUpdatesList::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* تحسين مظهر الحقول المطلوبة */
input[required]:invalid {
    border-color: #ef4444;
}

input[required]:valid {
    border-color: #10b981;
}

/* تنسيق رسالة عدم وجود تحديثات */
#noDispensingUpdates {
    animation: fadeIn 0.3s ease-in-out;
}

/* تحسين مظهر التواريخ */
input[type="datetime-local"] {
    direction: ltr;
    text-align: right;
    font-family: 'Cairo', sans-serif;
}

/* تنسيق أزرار الحذف */
.delete-update-btn {
    transition: all 0.2s ease;
}

.delete-update-btn:hover {
    background-color: rgba(239, 68, 68, 0.1);
    border-radius: 4px;
    padding: 4px 8px;
}
